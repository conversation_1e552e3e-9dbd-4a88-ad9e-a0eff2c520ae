2025-07-30 09:00:19.804 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-30 09:00:19.854 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_221 on USER-20230226QO with PID 12176 (D:\IdeaProject\maizi_edu_sys\target\classes started by Administrator in D:\IdeaProject\maizi_edu_sys)
2025-07-30 09:00:19.870 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-30 09:00:21.233 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 09:00:21.236 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 09:00:21.304 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 09:00:21.305 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 55 ms. Found 0 Redis repository interfaces.
2025-07-30 09:00:21.940 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-30 09:00:21.948 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-07-30 09:00:21.949 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 09:00:21.949 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-30 09:00:22.178 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:00:22.179 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2221 ms
2025-07-30 09:00:22.335 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-07-30 09:00:22.336 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-07-30 09:00:22.337 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-07-30 09:00:22.339 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=D:\IdeaProject\maizi_edu_sys\.\.\uploads, avatar=D:\IdeaProject\maizi_edu_sys\.\.\uploads\avatars
2025-07-30 09:00:22.366 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:00:22.479 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-07-30 09:00:22.879 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:22.883 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:22.887 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:00:22.913 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:00:22.916 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:00:22.932 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:22.933 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:22.934 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:22.937 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:00:23.051 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:23.052 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:23.052 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:23.052 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:23.053 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:23.053 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:23.053 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:23.054 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:24.871 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-07-30 09:00:24.875 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-07-30 09:00:25.896 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-07-30 09:00:25.982 [main] INFO  com.edu.maizi_edu_sys.util.MineRuApiClient - MineRU API客户端初始化完成，baseUrl: https://mineru.net, timeout: 60s
2025-07-30 09:00:26.065 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:26.188 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:00:26.188 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:00:26.272 [main] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 题目修正服务初始化完成，批处理大小: 3
2025-07-30 09:00:26.323 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 09:00:26.604 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-30 09:00:26.743 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - RedisTemplate configured successfully
2025-07-30 09:00:26.788 [main] INFO  com.edu.maizi_edu_sys.config.UserStatsCacheConfig - 用户统计缓存管理器初始化完成: maxSize=1000, expireMinutes=30
2025-07-30 09:00:27.260 [main] INFO  org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-30 09:00:27.282 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:D:\IdeaProject\maizi_edu_sys\.\.\uploads\
2025-07-30 09:00:27.719 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 09:00:27.769 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-07-30 09:00:27.787 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-07-30 09:00:27.789 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-07-30 09:00:27.789 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@74e4be21]]
2025-07-30 09:00:27.790 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-07-30 09:00:27.803 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 8.54 seconds (JVM running for 10.506)
2025-07-30 09:00:27.807 [main] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 题目自动校对独立服务启动，调度间隔 1 分钟，批大小 3
2025-07-30 09:00:27.840 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377706
2025-07-30 09:00:27.856 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:00:29.776 [RMI TCP Connection(3)-***********] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 09:00:29.777 [RMI TCP Connection(3)-***********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 09:00:29.780 [RMI TCP Connection(3)-***********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-30 09:01:09.929 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.HomeController - 访问首页
2025-07-30 09:01:10.916 [http-nio-8081-exec-8] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-28T06:54:04Z. Current time: 2025-07-30T01:01:10Z, a difference of 151626914 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzeHEiLCJpYXQiOjE3NTM1OTkyNDQsImV4cCI6MTc1MzY4NTY0NH0.FNpQ8qHYlKA_Wll9tnLxP9D1dKklVNEO1_kUtlSZdY1GgQAGRdJlyJujUabrD2BftEnLf5eKVPBJdEcx0JSQJw]
2025-07-30 09:01:10.917 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/user/info
2025-07-30 09:01:10.924 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - API request without token: /api/activities/page
2025-07-30 09:01:11.097 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - API request without token: /api/activities/statistics
2025-07-30 09:01:11.128 [http-nio-8081-exec-7] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /user/info
2025-07-30 09:01:11.193 [http-nio-8081-exec-6] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/users/current
2025-07-30 09:01:13.040 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 372
2025-07-30 09:01:13.045 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回有效JSON，长度: 372
2025-07-30 09:01:13.045 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 解析到 1 项修正建议，其中 1 项有效
2025-07-30 09:01:13.073 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.CorrectionApprovalService - 保存待审核修正记录，ID: 2341, 日期: 2025-07-30, 过期时间: 2025-09-28 09:01:13
2025-07-30 09:01:13.074 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 已保存 1 条修正建议待审核，审核ID: 2341
2025-07-30 09:01:13.077 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377709
2025-07-30 09:01:13.080 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377709的重试计数
2025-07-30 09:01:13.109 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:01:27.820 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377709
2025-07-30 09:01:27.822 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:01:49.460 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:01:49.460 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:01:49.460 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:01:49.460 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:01:49.461 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377712
2025-07-30 09:01:49.462 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377712的重试计数
2025-07-30 09:01:49.472 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:02:27.831 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377712
2025-07-30 09:02:27.833 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:02:55.210 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:02:55.210 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:02:55.210 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:02:55.210 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:02:55.212 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377715
2025-07-30 09:02:55.213 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377715的重试计数
2025-07-30 09:02:55.223 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:03:27.844 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377715
2025-07-30 09:03:27.846 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:03:31.163 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.HomeController - 访问首页
2025-07-30 09:03:31.410 [http-nio-8081-exec-2] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-28T06:54:04Z. Current time: 2025-07-30T01:03:31Z, a difference of 151767410 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzeHEiLCJpYXQiOjE3NTM1OTkyNDQsImV4cCI6MTc1MzY4NTY0NH0.FNpQ8qHYlKA_Wll9tnLxP9D1dKklVNEO1_kUtlSZdY1GgQAGRdJlyJujUabrD2BftEnLf5eKVPBJdEcx0JSQJw]
2025-07-30 09:03:31.411 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/user/info
2025-07-30 09:03:31.448 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - API request without token: /api/activities/page
2025-07-30 09:03:31.631 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - API request without token: /api/activities/statistics
2025-07-30 09:03:31.678 [http-nio-8081-exec-4] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /user/info
2025-07-30 09:03:31.737 [http-nio-8081-exec-10] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/users/current
2025-07-30 09:03:33.955 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - API request without token: /api/activities/log
2025-07-30 09:03:46.477 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:03:46.477 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:03:46.477 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:03:46.477 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:03:46.478 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377718
2025-07-30 09:03:46.479 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377718的重试计数
2025-07-30 09:03:46.489 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:04:27.853 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377718
2025-07-30 09:04:27.855 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:04:44.418 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:04:44.418 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:04:44.418 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:04:44.418 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:04:44.420 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377721
2025-07-30 09:04:44.421 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377721的重试计数
2025-07-30 09:04:44.431 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:05:27.859 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377721
2025-07-30 09:05:27.862 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:05:50.859 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 265
2025-07-30 09:05:50.859 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回有效JSON，长度: 265
2025-07-30 09:05:50.860 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 解析到 1 项修正建议，其中 1 项有效
2025-07-30 09:05:50.865 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.CorrectionApprovalService - 保存待审核修正记录，ID: 2342, 日期: 2025-07-30, 过期时间: 2025-09-28 09:05:50
2025-07-30 09:05:50.865 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 已保存 1 条修正建议待审核，审核ID: 2342
2025-07-30 09:05:50.866 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377724
2025-07-30 09:05:50.867 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377724的重试计数
2025-07-30 09:05:50.878 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:06:27.861 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377724
2025-07-30 09:06:27.863 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:06:44.916 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-07-30 09:06:44.916 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@74e4be21]]
2025-07-30 09:06:44.916 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-07-30 09:06:45.836 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - TopicCorrectionRunner 即将关闭，等待正在执行的任务完成...
2025-07-30 09:06:45.837 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-07-30 09:06:45.840 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-30 09:06:45.836 [correction-exec(core=1,max=1)-1] ERROR com.edu.maizi_edu_sys.service.TopicCorrectionService - 处理批次时发生错误
java.lang.RuntimeException: AI服务调用异常: null
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:77) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:142) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63dc935a.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: java.lang.InterruptedException
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:347) ~[?:1.8.0_221]
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1915) ~[?:1.8.0_221]
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:72) ~[classes/:?]
	... 17 more
2025-07-30 09:06:45.850 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-30 09:06:45.851 [correction-exec(core=1,max=1)-1] WARN  com.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Connection com.mysql.cj.jdbc.ConnectionImpl@6aa09b91 marked as broken because of SQLSTATE(08003), ErrorCode(0)
java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1649) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1565) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:86) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:88) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor134.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy182.prepare(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:87) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor138.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy181.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor141.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy125.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy151.update(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:265) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63dc935a.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_221]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_221]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1580) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	... 52 more
2025-07-30 09:06:54.344 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-30 09:06:54.380 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_221 on USER-20230226QO with PID 2576 (D:\IdeaProject\maizi_edu_sys\target\classes started by Administrator in D:\IdeaProject\maizi_edu_sys)
2025-07-30 09:06:54.409 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-30 09:06:55.508 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 09:06:55.510 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 09:06:55.560 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 09:06:55.561 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 37 ms. Found 0 Redis repository interfaces.
2025-07-30 09:06:56.117 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-30 09:06:56.124 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-07-30 09:06:56.124 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 09:06:56.124 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-30 09:06:56.352 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:06:56.352 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1904 ms
2025-07-30 09:06:56.505 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-07-30 09:06:56.506 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-07-30 09:06:56.507 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-07-30 09:06:56.509 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=D:\IdeaProject\maizi_edu_sys\.\.\uploads, avatar=D:\IdeaProject\maizi_edu_sys\.\.\uploads\avatars
2025-07-30 09:06:56.532 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:06:56.626 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-07-30 09:06:56.965 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:56.966 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:56.969 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:06:56.990 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:06:56.992 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:06:57.006 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.007 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.007 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.010 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:06:57.102 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.102 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.102 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.103 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.103 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.103 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.104 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.104 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:58.698 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-07-30 09:06:58.700 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-07-30 09:06:59.574 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-07-30 09:06:59.654 [main] INFO  com.edu.maizi_edu_sys.util.MineRuApiClient - MineRU API客户端初始化完成，baseUrl: https://mineru.net, timeout: 60s
2025-07-30 09:06:59.721 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:59.837 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:06:59.838 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:06:59.893 [main] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 题目修正服务初始化完成，批处理大小: 3
2025-07-30 09:06:59.922 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 09:07:00.081 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-30 09:07:00.347 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - RedisTemplate configured successfully
2025-07-30 09:07:00.374 [main] INFO  com.edu.maizi_edu_sys.config.UserStatsCacheConfig - 用户统计缓存管理器初始化完成: maxSize=1000, expireMinutes=30
2025-07-30 09:07:00.598 [main] INFO  org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-30 09:07:00.619 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:D:\IdeaProject\maizi_edu_sys\.\.\uploads\
2025-07-30 09:07:01.016 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 09:07:01.064 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-07-30 09:07:01.078 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-07-30 09:07:01.080 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-07-30 09:07:01.080 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@77d54a41]]
2025-07-30 09:07:01.081 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-07-30 09:07:01.092 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 7.111 seconds (JVM running for 8.904)
2025-07-30 09:07:01.097 [main] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 题目自动校对独立服务启动，调度间隔 1 分钟，批大小 3
2025-07-30 09:07:01.114 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377724
2025-07-30 09:07:01.119 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:07:02.246 [RMI TCP Connection(1)-***********] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 09:07:02.246 [RMI TCP Connection(1)-***********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 09:07:02.249 [RMI TCP Connection(1)-***********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-30 09:07:24.078 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:07:24.078 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:07:24.078 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:07:24.078 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:07:24.080 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377727
2025-07-30 09:07:24.081 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377727的重试计数
2025-07-30 09:07:24.111 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:07:34.653 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-07-30 09:07:34.653 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@77d54a41]]
2025-07-30 09:07:34.653 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-07-30 09:07:35.610 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - TopicCorrectionRunner 即将关闭，等待正在执行的任务完成...
2025-07-30 09:07:35.611 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-07-30 09:07:35.614 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-30 09:07:35.623 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-30 09:07:43.704 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-30 09:07:43.768 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_221 on USER-20230226QO with PID 17704 (D:\IdeaProject\maizi_edu_sys\target\classes started by Administrator in D:\IdeaProject\maizi_edu_sys)
2025-07-30 09:07:43.771 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-30 09:07:44.709 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 09:07:44.711 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 09:07:44.759 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 09:07:44.760 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 38 ms. Found 0 Redis repository interfaces.
2025-07-30 09:07:45.272 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-30 09:07:45.279 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-07-30 09:07:45.279 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 09:07:45.279 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-30 09:07:45.495 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:07:45.496 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1689 ms
2025-07-30 09:07:45.638 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-07-30 09:07:45.639 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-07-30 09:07:45.639 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-07-30 09:07:45.642 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=D:\IdeaProject\maizi_edu_sys\.\.\uploads, avatar=D:\IdeaProject\maizi_edu_sys\.\.\uploads\avatars
2025-07-30 09:07:45.664 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:07:45.754 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-07-30 09:07:46.041 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.041 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.045 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:07:46.059 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:07:46.061 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:07:46.071 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.071 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.071 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.074 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:07:46.163 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.164 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.164 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.164 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.165 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.165 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.165 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.166 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:47.732 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-07-30 09:07:47.735 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-07-30 09:07:48.573 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-07-30 09:07:48.655 [main] INFO  com.edu.maizi_edu_sys.util.MineRuApiClient - MineRU API客户端初始化完成，baseUrl: https://mineru.net, timeout: 60s
2025-07-30 09:07:48.722 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:48.833 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:07:48.834 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:07:48.891 [main] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 题目修正服务初始化完成，批处理大小: 3
2025-07-30 09:07:48.922 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 09:07:49.212 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-30 09:07:49.318 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - RedisTemplate configured successfully
2025-07-30 09:07:49.346 [main] INFO  com.edu.maizi_edu_sys.config.UserStatsCacheConfig - 用户统计缓存管理器初始化完成: maxSize=1000, expireMinutes=30
2025-07-30 09:07:49.577 [main] INFO  org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-30 09:07:49.597 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:D:\IdeaProject\maizi_edu_sys\.\.\uploads\
2025-07-30 09:07:49.984 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 09:07:50.030 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-07-30 09:07:50.045 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-07-30 09:07:50.047 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-07-30 09:07:50.047 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5600a278]]
2025-07-30 09:07:50.047 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-07-30 09:07:50.059 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 6.712 seconds (JVM running for 8.478)
2025-07-30 09:07:50.063 [main] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 题目自动校对独立服务启动，调度间隔 1 分钟，批大小 3
2025-07-30 09:07:50.081 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377727
2025-07-30 09:07:50.086 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:07:51.864 [RMI TCP Connection(2)-***********] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 09:07:51.865 [RMI TCP Connection(2)-***********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 09:07:51.867 [RMI TCP Connection(2)-***********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-30 09:08:28.797 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 160
2025-07-30 09:08:28.800 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回有效JSON，长度: 160
2025-07-30 09:08:28.802 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 解析到 1 项修正建议，其中 1 项有效
2025-07-30 09:08:28.820 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.CorrectionApprovalService - 保存待审核修正记录，ID: 2343, 日期: 2025-07-30, 过期时间: 2025-09-28 09:08:28
2025-07-30 09:08:28.821 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 已保存 1 条修正建议待审核，审核ID: 2343
2025-07-30 09:08:28.822 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377730
2025-07-30 09:08:28.823 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377730的重试计数
2025-07-30 09:08:28.851 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:08:50.078 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377730
2025-07-30 09:08:50.081 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:09:18.067 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:09:18.067 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:09:18.067 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:09:18.068 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:09:18.072 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377733
2025-07-30 09:09:18.074 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377733的重试计数
2025-07-30 09:09:18.098 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:09:50.099 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377733
2025-07-30 09:09:50.106 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:10:06.921 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:10:06.921 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:10:06.921 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:10:06.921 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:10:06.923 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377736
2025-07-30 09:10:06.925 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377736的重试计数
2025-07-30 09:10:06.938 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:10:50.099 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377736
2025-07-30 09:10:50.102 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:11:06.805 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:11:06.805 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:11:06.805 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:11:06.805 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:11:06.807 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377739
2025-07-30 09:11:06.809 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377739的重试计数
2025-07-30 09:11:06.821 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:11:50.108 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377739
2025-07-30 09:11:50.109 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:12:19.753 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:12:19.753 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:12:19.753 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:12:19.753 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:12:19.754 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377742
2025-07-30 09:12:19.755 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377742的重试计数
2025-07-30 09:12:19.764 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:12:50.111 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377742
2025-07-30 09:12:50.114 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:13:09.412 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:13:09.412 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:13:09.413 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:13:09.413 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:13:09.414 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377745
2025-07-30 09:13:09.415 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377745的重试计数
2025-07-30 09:13:09.425 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:13:49.562 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-30 09:13:49.648 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_221 on USER-20230226QO with PID 14120 (D:\IdeaProject\maizi_edu_sys\target\classes started by Administrator in D:\IdeaProject\maizi_edu_sys)
2025-07-30 09:13:49.652 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-30 09:13:50.123 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377745
2025-07-30 09:13:50.125 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:13:50.917 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 09:13:50.919 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 09:13:50.972 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 09:13:50.973 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 40 ms. Found 0 Redis repository interfaces.
2025-07-30 09:13:51.709 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-30 09:13:51.729 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-07-30 09:13:51.730 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 09:13:51.731 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-30 09:13:52.129 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:13:52.129 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2433 ms
2025-07-30 09:13:52.305 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-07-30 09:13:52.307 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-07-30 09:13:52.307 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-07-30 09:13:52.310 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=D:\IdeaProject\maizi_edu_sys\.\.\uploads, avatar=D:\IdeaProject\maizi_edu_sys\.\.\uploads\avatars
2025-07-30 09:13:52.337 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:13:52.453 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-07-30 09:13:52.786 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.786 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.790 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:13:52.806 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:13:52.808 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:13:52.822 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.822 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.823 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.827 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:13:52.911 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.912 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.912 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.912 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.913 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.913 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.913 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.914 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:54.614 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-07-30 09:13:54.618 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-07-30 09:13:55.799 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-07-30 09:13:55.904 [main] INFO  com.edu.maizi_edu_sys.util.MineRuApiClient - MineRU API客户端初始化完成，baseUrl: https://mineru.net, timeout: 60s
2025-07-30 09:13:55.997 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:56.101 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:13:56.102 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:13:56.160 [main] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 题目修正服务初始化完成，批处理大小: 3
2025-07-30 09:13:56.259 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 09:13:56.633 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-30 09:13:56.923 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - RedisTemplate configured successfully
2025-07-30 09:13:56.952 [main] INFO  com.edu.maizi_edu_sys.config.UserStatsCacheConfig - 用户统计缓存管理器初始化完成: maxSize=1000, expireMinutes=30
2025-07-30 09:13:57.206 [main] INFO  org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-30 09:13:57.231 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:D:\IdeaProject\maizi_edu_sys\.\.\uploads\
2025-07-30 09:13:57.685 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 09:13:57.738 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-07-30 09:13:57.746 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8081 is already in use
2025-07-30 09:13:57.757 [main] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - TopicCorrectionRunner 即将关闭，等待正在执行的任务完成...
2025-07-30 09:13:57.759 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-07-30 09:13:57.762 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-30 09:13:57.773 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-30 09:13:57.780 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8081"]
2025-07-30 09:13:57.780 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-30 09:13:57.783 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8081"]
2025-07-30 09:13:57.783 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8081"]
2025-07-30 09:13:57.794 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-30 09:13:57.812 [main] ERROR org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-07-30 09:14:00.361 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-07-30 09:14:00.361 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5600a278]]
2025-07-30 09:14:00.361 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-07-30 09:14:01.229 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - TopicCorrectionRunner 即将关闭，等待正在执行的任务完成...
2025-07-30 09:14:01.231 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-07-30 09:14:01.233 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-30 09:14:01.230 [correction-exec(core=1,max=1)-1] ERROR com.edu.maizi_edu_sys.service.TopicCorrectionService - 处理批次时发生错误
java.lang.RuntimeException: AI服务调用异常: null
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:77) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:142) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63011bcb.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: java.lang.InterruptedException
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:347) ~[?:1.8.0_221]
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1915) ~[?:1.8.0_221]
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:72) ~[classes/:?]
	... 17 more
2025-07-30 09:14:01.243 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-30 09:14:01.244 [correction-exec(core=1,max=1)-1] WARN  com.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Connection com.mysql.cj.jdbc.ConnectionImpl@e3794ec marked as broken because of SQLSTATE(08003), ErrorCode(0)
java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1649) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1565) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:86) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:88) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor134.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy182.prepare(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:87) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor137.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy181.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor139.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy125.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy151.update(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:265) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63011bcb.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_221]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_221]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1580) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	... 52 more
